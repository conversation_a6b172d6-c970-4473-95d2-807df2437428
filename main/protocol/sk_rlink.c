/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_rlink.c
 * @description: Relay链路, 用于和转发模块间传递音频和控制数据.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#include <stdint.h>
#include <stdlib.h>
#include <arpa/inet.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "driver/i2s_std.h"
#include "driver/gpio.h"
#include "esp_check.h"
#include "sdkconfig.h"
#include "esp_log.h"

#include <string.h>
#include <unistd.h>
#include <sys/socket.h>
#include <errno.h>
#include <netdb.h>            // struct addrinfo
#include <arpa/inet.h>
#include "esp_netif.h"
#include "esp_timer.h" 
#include "sk_common.h"
#include "sk_frame.h"
#include "sk_clink.h"
#include "sk_audio.h"
#include "sk_audio_buffer.h"
#include "sk_rlink.h"
#include "sk_sm.h"
#include "sk_opus_enc.h"
#include "sk_opus_dec.h"
#include "sk_os.h"
#include "sk_board.h"

#define RLINK_MSG_QUEUE_SIZE 64
#define RLINK_RX_BUFF_SIZE  4096
#define TAG "SkRlink"
#define RLINK_TIMEOUT_CNT 10
#define AUDIO_8_BITS 0

enum {
    RLINK_SOCK_NOT_RDY = 0,
    RLINK_SOCK_RDY = 1,
};

enum {
    RLINK_CONNECTING = 0,
    RLINK_DISCONNECT = 1,
};

enum {
    RLINK_PEER_LOGOUT = 0,
    RLINK_PEER_LOGIN = 1,
};

enum {
    RLINK_EXIT_BY_CLOSE = 0,
    RLINK_EXIT_BY_CLOSE_BY_PEER = 1,
    RLINK_EXIT_BY_NEW_LINK = 2,
    RLINK_EXIT_BY_NEW_AGENT = 3,
};

typedef struct {
    uint8_t event;      // 事件
    uint8_t src;        // 事件源模块
    uint16_t param1;    // 参数
    uint32_t timestamp;
    void    *arg;       // 数据长度
} RlinkMsg;

enum {
    RLINK_MODE_AGENT = 0,
    RLINK_MODE_AGENT_CALLEE_MATCH = 1,
};

typedef struct {
    uint32_t linkFlag;
    uint32_t disconnectFlag;
    uint8_t linkMode;
    uint32_t rxExitReason;
    char serverIp[16];
    uint16_t port;
    uint16_t sockRdy;
    uint8_t linkCheckLoopCnt;
    uint8_t taskFlag;
    int sock;
    char reqServerIp[16];
    uint16_t reqPort;
    char callAgentIp[16];
    uint16_t callAgentPort;
    uint16_t sessionID;
    uint16_t seqID;
    int32_t buffCnt;
    QueueHandle_t msgQueue;
    esp_timer_handle_t timer;
    void *recordQueue;
    TaskHandle_t txTaskHandle;
    TaskHandle_t rxTaskHandle;
    int32_t bytesPerSample;
    int32_t txChunkSize;
    int32_t rxChunkSize;
    int32_t roleMode;
    int32_t offhookSendCnt;
    CtrlMsg ctrlMsg;
    uint32_t rxTimeoutCnt;

    uint32_t sendSuccBytes;
    uint32_t sendFailBytes;
    uint32_t sendDataFailCnt;
    uint32_t sendSuccCnt;
    uint32_t sendDataContinusFailCnt;
    uint32_t statRecordBufFail;
    uint32_t statRecordEnqueueFail;
    uint32_t statRecordEnqueueCnt;

    uint32_t recvDataCnt;
    uint32_t recvBufFailCnt;
    uint32_t recvEnqueueFailCnt;
    uint32_t recvEnqueueCnt;
    uint32_t playDataCnt;
    uint32_t playNoDataCnt;
    SkStateHandler handler;
    void *decPrivate;
    SkRlinkCodedDataCallback codedDataCallback;
    SkRlinkCodedDataEndCallback codedDataEndCallback;
} RlinkCtrlInfo;

RlinkCtrlInfo g_rlinkCtrl = {
    .linkFlag = RLINK_LINK_STOP,
    .disconnectFlag = RLINK_CONNECTING,
    .serverIp = "***************",
    .port = 9528, 
    .callAgentIp = "***********",
    .callAgentPort =50512,
    .sockRdy = RLINK_SOCK_NOT_RDY,
    .linkCheckLoopCnt = 0,
    .taskFlag = RLINK_TASK_RUN,
    .sock = -1,
    .msgQueue = NULL,
    .timer = NULL,
};

int RlinkConnect(RlinkCtrlInfo *ctrl) {
	int ret, sock;
	uint32_t addr;
    struct sockaddr_in ack_sock_addr;

    memset(&ack_sock_addr, 0, sizeof(struct sockaddr));
    ack_sock_addr.sin_family = AF_INET;
    sock = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);

	addr = inet_addr(ctrl->serverIp);
	memcpy((char *)&ack_sock_addr.sin_addr, (char *)&addr, sizeof(addr));
    ack_sock_addr.sin_port = htons(ctrl->port);

    // 设置 send 超时时间（单位：毫秒）
    struct timeval send_timeout = {0, 100000};
    if (setsockopt(sock, SOL_SOCKET, SO_SNDTIMEO, &send_timeout, sizeof(send_timeout)) < 0) {
        perror("Failed to set SO_SNDTIMEO");
        close(sock);
        return -1;
    }

    // 设置 recv 超时时间（单位：毫秒）
    struct timeval recv_timeout = {0, 100000};
    if (setsockopt(sock, SOL_SOCKET, SO_RCVTIMEO, &recv_timeout, sizeof(recv_timeout)) < 0) {
        perror("Failed to set SO_RCVTIMEO");
        close(sock);
        return -1;
    }

	ret = connect(sock, (struct sockaddr *)&ack_sock_addr, sizeof(struct sockaddr));
    if (ret != 0) {
        ESP_LOGI(TAG, "connect ack failed! socket num=%d", sock);
        closesocket(sock);
        return SK_RET_FAIL;
    }
	ctrl->sock = sock;
    ctrl->linkCheckLoopCnt = RLINK_TIMEOUT_CNT;
    ctrl->buffCnt = 0;
    ctrl->offhookSendCnt = 0;
	ctrl->sockRdy = RLINK_SOCK_RDY;
    ctrl->sendSuccBytes = 0;
    ctrl->sendFailBytes = 0;
    ctrl->sendDataFailCnt = 0;
    ctrl->sendSuccCnt = 0;
    ctrl->sendDataContinusFailCnt = 0;
    ctrl->statRecordBufFail = 0;
    ctrl->statRecordEnqueueFail = 0;
    ctrl->statRecordEnqueueCnt = 0;

    ctrl->recvDataCnt = 0;
    ctrl->recvBufFailCnt = 0;
    ctrl->recvEnqueueFailCnt = 0;
    ctrl->recvEnqueueCnt = 0;
    ctrl->playDataCnt = 0;
    ctrl->playNoDataCnt = 0;
    ctrl->rxTimeoutCnt = 0;

    return SK_RET_SUCCESS;
}

void RlinkDisconnect(RlinkCtrlInfo *ctrl) {
	if (ctrl->sockRdy == RLINK_SOCK_RDY) {
		ctrl->sockRdy = RLINK_SOCK_NOT_RDY;
        ctrl->linkCheckLoopCnt = RLINK_TIMEOUT_CNT;
	    closesocket(ctrl->sock);
		ctrl->sock = -1;
	}

    return;
}

int RlinkSendInnerEvent(RlinkCtrlInfo *ctrl, uint8_t event, uint16_t param1) {
    RlinkMsg msg;
    if (g_rlinkCtrl.msgQueue == NULL) {
        ESP_LOGI(TAG, "send inner event %d, msgQueue is null", event);
        return SK_RET_FAIL;
    }
    msg.event = event;
    msg.src = 0;
    msg.arg = NULL;
    msg.param1 = param1;
    if (xQueueSend(ctrl->msgQueue, &msg, portMAX_DELAY) != pdPASS) {
        ESP_LOGI(TAG, "send inner event %d success, error", event);
        return SK_RET_FAIL;
    }
    ESP_LOGI(TAG, "send inner event %d success", event);
    return SK_RET_SUCCESS;
}

int SkRlinkEventNotify(uint8_t event, uint16_t param1) {
    RlinkMsg msg;
    if (g_rlinkCtrl.msgQueue == NULL) {
        return SK_RET_FAIL;
    }
    msg.event = event;
    msg.param1 = param1;
    msg.arg = NULL;
    xQueueSend(g_rlinkCtrl.msgQueue, &msg, portMAX_DELAY);
    return SK_RET_SUCCESS;
}

int SkRlinkSendAudioData(SkAudioBuf *audioBuf, uint32_t timestamp) {
    RlinkMsg msg;
    if (g_rlinkCtrl.msgQueue == NULL) {
        return SK_RET_FAIL;
    }
    msg.event = RLINK_EVENT_TX_DATA;
    msg.src = 0;
    msg.arg = audioBuf;
    msg.timestamp = timestamp;
    if (xQueueSend(g_rlinkCtrl.msgQueue, &msg, 0) != pdPASS) {
        return SK_RET_FAIL;
    }
    return SK_RET_SUCCESS;
}

int RlinkSendRemoteMsg(RlinkCtrlInfo *ctrl, void *data, size_t size) {
    int ret;

    ret = send(ctrl->sock, (char *)data, size, 0);
    if (ret < 0) {
        ctrl->sendFailBytes += size;
    } else {
        ctrl->sendSuccBytes += ret;
        ctrl->sendFailBytes += (size - ret);
    }
    if (ret != size) {
        return SK_RET_FAIL;
    }
    return SK_RET_SUCCESS;
}

inline uint16_t RlinkGetTxSeqId(RlinkCtrlInfo *ctrl) {
    ctrl->seqID++;
    return ctrl->seqID;
}

int RlinkSendMsgRegisterTerm(RlinkCtrlInfo *ctrl) {
    CtrlMsgFrame msg;
    memcpy(&msg.ctrlMsg, &ctrl->ctrlMsg, sizeof(CtrlMsg));
    if (ctrl->roleMode == SK_ROLE_MODE_CALLER) {
        EncodeRelayReqMsg(&msg, MSG_TERM_TO_RELAY_CALLER_REQUEST);
    } else {
        EncodeRelayReqMsg(&msg, MSG_TERM_TO_RELAY_CALLEE_REQUEST);
    }
    msg.frameHead.seqID = htons(RlinkGetTxSeqId(ctrl));
    return RlinkSendRemoteMsg(ctrl, &msg, sizeof(CtrlMsgFrame));
}

int RlinkSendOnhookOrOffhook(RlinkCtrlInfo *ctrl, int msgType) {
    CtrlMsgFrame msg;
    EncodeTermOnhookOffhookMsg(&msg, msgType);
    msg.frameHead.seqID = htons(RlinkGetTxSeqId(ctrl));
    return RlinkSendRemoteMsg(ctrl, &msg, sizeof(CtrlMsgFrame));
}


int RlinkAlignAudio(RlinkCtrlInfo *ctrl) {
    CtrlMsgFrame msg;
    if (ctrl->sockRdy != RLINK_SOCK_RDY) {
        return SK_RET_SUCCESS;
    }
    ESP_LOGI(TAG, "Send align audio msg!");
    EncodeRelayAlignAudioMsg(&msg);
    msg.frameHead.seqID = htons(RlinkGetTxSeqId(ctrl));
    return RlinkSendRemoteMsg(ctrl, &msg, sizeof(CtrlMsgFrame));
}

int32_t RlinkSendAudioFrame(RlinkCtrlInfo *ctrl, DataFrame *frame, uint32_t dataLen, uint32_t timestamp) {
    int ret;
    uint16_t seq;
    SkAudioUplinkTimeRecord *timeRecord = (SkAudioUplinkTimeRecord *) frame->timeRecord;

    seq = RlinkGetTxSeqId(ctrl);
    timeRecord->seq = seq;
    timeRecord->len = dataLen;
    timeRecord->dataFlag = frame->data[1];
    timeRecord->encDoneTick = timestamp;
    timeRecord->encTxTick = SkOsGetTickCnt();
    timeRecord->relayRxTick = 0;
    timeRecord->relayTxTick = 0;
    timeRecord->decRxTick = 0;
    timeRecord->decStartTick = 0;
    timeRecord->playTick = 0;
    EncodeRelayDataMsg(frame, dataLen + sizeof(frame->timeRecord));
    frame->frameHead.seqID = htons(seq);

    ret = RlinkSendRemoteMsg(ctrl, frame, dataLen + sizeof(FrameHead) + sizeof(frame->timeRecord));
    if (ret != SK_RET_SUCCESS) {
        ctrl->sendDataFailCnt++;
        ctrl->sendDataContinusFailCnt++;
        if (ctrl->sendDataContinusFailCnt >= 8) {
            ctrl->linkFlag = RLINK_LINK_STOP;
        }
    } else {
        ctrl->sendDataContinusFailCnt = 0;
        ctrl->sendSuccCnt++;
    }

    return ret;
}

int RlinkSendAudioData(RlinkCtrlInfo *ctrl, SkAudioBuf *audioBuf, uint32_t timestamp) {
    int ret = SK_RET_SUCCESS;

    if (ctrl->linkFlag == RLINK_LINK_RUN) {
        ret = RlinkSendAudioFrame(ctrl, (DataFrame *)audioBuf->data,
            audioBuf->length, timestamp);
    }    
    SkAudioBufferPutFree(ctrl->recordQueue, audioBuf);

    return ret;
}

void RlinkSetCallReqInfo(uint32_t roleMode, uint8_t serverIp[4], uint16_t port, void *ctrlMsg) {
    memcpy(&g_rlinkCtrl.ctrlMsg, ctrlMsg, sizeof(CtrlMsg));
    sprintf((char *)g_rlinkCtrl.reqServerIp, "%u.%u.%u.%u", serverIp[0], serverIp[1], serverIp[2], serverIp[3]);
    g_rlinkCtrl.reqPort = port;
    g_rlinkCtrl.roleMode = roleMode;
}

int32_t RlinkStartCall(RlinkCtrlInfo *ctrl) {
    if (ctrl->sockRdy == RLINK_SOCK_RDY) {
        ESP_LOGI(TAG, "RlinkConnect already connected!");
        SkSmSendEvent(ctrl->handler, SM_EVENT_LINK, SM_EVENT_RLINK_CONNECT, ctrl->sessionID, 0);
        return SK_RET_FAIL;
    }
    memcpy(ctrl->serverIp, ctrl->reqServerIp, 16);
    ctrl->port = ctrl->reqPort;
    if (RlinkConnect(ctrl) != SK_RET_SUCCESS) {
        ESP_LOGI(TAG, "RlinkConnect fail!");
        RlinkSendInnerEvent(ctrl, RLINK_EVENT_RX_EXIT, RLINK_EXIT_BY_CLOSE);
        return SK_RET_FAIL;
    }
    ESP_LOGI(TAG, "RlinkConnect success!");
    if (RlinkSendMsgRegisterTerm(ctrl) != SK_RET_SUCCESS) {
        ESP_LOGI(TAG, "RlinkSendMsgRegisterTerm fail!");
        RlinkSendInnerEvent(ctrl, RLINK_EVENT_RX_EXIT, RLINK_EXIT_BY_CLOSE);
        return SK_RET_FAIL;
    }
    ctrl->disconnectFlag = RLINK_CONNECTING;
    ctrl->linkFlag = RLINK_LINK_RUN;
    SkSmSendEvent(ctrl->handler, SM_EVENT_LINK, SM_EVENT_RLINK_CONNECT, ctrl->sessionID, 0);

    return SK_RET_SUCCESS;
}

int32_t RlinkSendCalleeMatchReq(RlinkCtrlInfo *ctrl) {
    int32_t ret = SK_RET_FAIL;
    CtrlFrameCalleeMatch *msg = NULL;

    msg = (CtrlFrameCalleeMatch *)malloc(sizeof(CtrlFrameCalleeMatch));
    if (msg == NULL) {
        return ret;
    }
    EncodeTermToCloudAiMsg(msg, MSG_TERM_TO_CLOUD_AI_CALLEE_MATCH_REQUEST);
    msg->frameHead.seqID = htons(RlinkGetTxSeqId(ctrl));
    ret = RlinkSendRemoteMsg(ctrl, msg, sizeof(CtrlFrameCalleeMatch));
    free(msg);
    msg = NULL;

    return ret;
}

int32_t RlinkSendCmdAudio(RlinkCtrlInfo *ctrl) {
    int32_t ret;
    uint16_t len;
    SkCmdData *cmdData = NULL;
    DataFrame *frame = NULL;

    frame = (DataFrame *)malloc(sizeof(DataFrame));
    if (frame == NULL) {
        return SK_RET_FAIL;
    }
    for (int i = 0; i < 100; i++) {
        cmdData = SkOpusPopCmdAudio();
        if (cmdData == NULL) {
            ret = SK_RET_SUCCESS;
            break;
        }
        len = cmdData->frameSize + 4;
        cmdData->frameSize = htons(cmdData->frameSize);
        ret = RlinkSendAudioFrame(ctrl, frame, len, 0);
        if (ret != SK_RET_SUCCESS) {
            break;
        }
    }
    free(frame);
    frame = NULL;

    return ret;
}

void RlinkLinkCallAgent(RlinkCtrlInfo *ctrl) {
    if (ctrl->sockRdy == RLINK_SOCK_RDY) {
        ESP_LOGI(TAG, "RlinkConnect already connected!");
        return;
    }

    memcpy(ctrl->serverIp, ctrl->callAgentIp, 16);
    ctrl->port = ctrl->callAgentPort;
    ctrl->linkMode = RLINK_MODE_AGENT_CALLEE_MATCH;
    if (RlinkConnect(ctrl) != SK_RET_SUCCESS) {
        ESP_LOGI(TAG, "Rlink connect call agent fail!");
        RlinkSendInnerEvent(ctrl, RLINK_EVENT_RX_EXIT, RLINK_EXIT_BY_CLOSE);
        return;
    }
    ESP_LOGI(TAG, "Rlink connect call agent success!");
    if (RlinkSendCalleeMatchReq(ctrl) != SK_RET_SUCCESS) {
        ESP_LOGI(TAG, "RlinkSendCalleeMatchReq fail!");
        RlinkSendInnerEvent(ctrl, RLINK_EVENT_RX_EXIT, RLINK_EXIT_BY_CLOSE);
        return;
    }
    if (RlinkSendCmdAudio(ctrl) != SK_RET_SUCCESS) {
        ESP_LOGI(TAG, "RlinkSendCalleeMatchReq fail!");
        RlinkSendInnerEvent(ctrl, RLINK_EVENT_RX_EXIT, RLINK_EXIT_BY_CLOSE);
        return;
    }

    ctrl->disconnectFlag = RLINK_CONNECTING;
    ctrl->linkFlag = RLINK_LINK_RUN;

    return;
}

// 停止Rlink调用
void RlinkStopCall(RlinkCtrlInfo *ctrl) {
    // 如果sockRdy标志位为RLINK_SOCK_NOT_RDY，则直接返回
    if (ctrl->sockRdy == RLINK_SOCK_NOT_RDY) {
        return;
    }
    ESP_LOGI(TAG, "RlinkDisconnect success!");
    ESP_LOGI(TAG, "SendSuccBytes: %u, SendFailBytes: %u, SendFailCnt: %u(%u).", 
        ctrl->sendSuccBytes, ctrl->sendFailBytes, ctrl->sendDataFailCnt, ctrl->sendDataContinusFailCnt);
    RlinkDisconnect(ctrl);

    return;
}

void SkRlinkShowStat() {
    RlinkCtrlInfo *ctrl = &g_rlinkCtrl;
    ESP_LOGI(TAG, "Tx: EqFail=%u, BufFail=%u, EqCnt=%u, TxFail=%u, TxSucc=%u, TxBytes=%u, FailBytes=%u.", 
        ctrl->statRecordEnqueueFail, ctrl->statRecordBufFail, ctrl->statRecordEnqueueCnt, 
        ctrl->sendDataFailCnt, ctrl->sendSuccCnt, ctrl->sendSuccBytes, ctrl->sendFailBytes);
    ESP_LOGI(TAG, "Rx: RxCnt=%u, BufFail=%u, EqFail=%u, EqSucc=%u, PlaySucc=%u, PlayNo=%u, Timeout=%u.", 
        ctrl->recvDataCnt, ctrl->recvBufFailCnt, ctrl->recvEnqueueFailCnt, ctrl->recvEnqueueCnt, 
        ctrl->playDataCnt, ctrl->playNoDataCnt, ctrl->rxTimeoutCnt);
    return;
}

void RlinkTimerCallback(void* arg) {
    RlinkCtrlInfo *ctrl = (RlinkCtrlInfo *)arg;

    if (ctrl->sockRdy == RLINK_SOCK_NOT_RDY) {
        return;
    }
#if CONFIG_SHOW_RUN_STAT
    SkOpusDecStat();
    SkOpusEncShowStatus();
    SkSrShowStat();
    SkRlinkShowStat();
#endif
    return;
}

void RlinkStartTimer(RlinkCtrlInfo *ctrl) {
    const esp_timer_create_args_t timerArgs = {
        .callback = &RlinkTimerCallback,
        .arg = ctrl,
        .name = "RlinkTimer"
    };

    if (ctrl->timer != NULL) {
        return;
    }
    ESP_ERROR_CHECK(esp_timer_create(&timerArgs, &ctrl->timer));
    ESP_ERROR_CHECK(esp_timer_start_periodic(ctrl->timer, 1000 * 1000)); // 时间单位为微秒
    return;
}

void RlinkStopTimer(RlinkCtrlInfo *ctrl) {
    if (ctrl->timer == NULL) {
        return;
    }
    esp_err_t err = esp_timer_stop(ctrl->timer);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to stop timer: %s", esp_err_to_name(err));
    } else {
        ESP_LOGI(TAG, "Timer stopped successfully");
    }
    err = esp_timer_delete(ctrl->timer);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to delete timer: %s", esp_err_to_name(err));
    } else {
        ESP_LOGI(TAG, "Timer deleted successfully");
    }
    ctrl->timer = NULL;
    return;
}

int RlinkInit(SkStateHandler handler, int32_t bytesPerSample, int32_t txChunkSize, int32_t rxChunkSize) {
    RlinkCtrlInfo *ctrl = &g_rlinkCtrl;

    ctrl->handler = handler;
    ctrl->linkCheckLoopCnt = 0;
    ctrl->msgQueue = xQueueCreate(RLINK_MSG_QUEUE_SIZE, sizeof(RlinkMsg));
    ctrl->bytesPerSample = bytesPerSample;
    ctrl->txChunkSize = txChunkSize;
    ctrl->rxChunkSize = rxChunkSize;
    ctrl->recordQueue = SkCreateAudioQueue(4, sizeof(DataFrame), sizeof(FrameHead));
    RlinkStartTimer(ctrl);

    return SK_RET_SUCCESS;
}

void RlinkDeinit(void) {
    RlinkCtrlInfo *ctrl = &g_rlinkCtrl;

    RlinkStopTimer(ctrl);
    // 删除消息队列
    vQueueDelete(ctrl->msgQueue);
}

int RlinkCheckMsgValid(FrameHead *frameHead, uint8_t *frameType, size_t *payloadLen) {
    int ret = SK_RET_FAIL;
    uint16_t payloadLenTmp;

    if (ntohs(frameHead->headFlag) == FRAME_HEAD_FLAG &&
        ntohs(frameHead->headLen) == sizeof(FrameHead)) {
        payloadLenTmp = ntohs(frameHead->payloadLen);
        *frameType = frameHead->frameType;
        *payloadLen = payloadLenTmp;
        if (frameHead->frameType == FRAME_CMSG_TERM_AND_TERM) {
            if (payloadLenTmp == sizeof(SessionHead) + sizeof(CtrlMsg)) {
                ret = SK_RET_SUCCESS;
            }
        } else if (frameHead->frameType == FRAME_DPKT_TERM_AND_RELAY) {
            if (payloadLenTmp <= DPKT_DATA_LENGTH) {
                ret = SK_RET_SUCCESS;
            }
        } else if (frameHead->frameType == FRAME_CMSG_AGENT_AND_TERM) {
            ret = SK_RET_SUCCESS;
        }
    }

    return ret;
}

void RlinkPlayAudioData(RlinkCtrlInfo *ctrl, uint8_t *data, uint16_t payloadLen) {
    int32_t ret;
    DataFrame *frame = (DataFrame *)data;
    SkAudioDownlinkTimeRecord *timeRecord = (SkAudioDownlinkTimeRecord *)frame->timeRecord;
    SkAudioDownlinkTimeRecord audioTickRecord;

    ctrl->recvDataCnt++;
    if (ctrl->codedDataCallback == NULL) {
        return;
    }
    memcpy(&audioTickRecord, timeRecord, sizeof(SkAudioDownlinkTimeRecord));
    audioTickRecord.decRxTick = SkOsGetTickCnt();
    ret = ctrl->codedDataCallback(ctrl->decPrivate, ctrl->sessionID, 
        data + sizeof(FrameHead) + sizeof(frame->timeRecord), 
        payloadLen - sizeof(frame->timeRecord), &audioTickRecord);
    if (ret == SK_RET_SUCCESS) {
        ctrl->buffCnt++;
        ctrl->recvEnqueueCnt++;
    } else if (ret == SK_RET_NO_MEMORY) {
        ctrl->recvBufFailCnt++;
    } else if (ret == SK_RET_INVALID_PARAM) {
        ctrl->recvEnqueueFailCnt++;
    }

    return;
}

/**
 * @brief 处理接收到的控制消息帧
 *
 * 该函数接收一个指向数据缓冲区的指针和数据长度，解析并处理控制消息帧。
 *
 * @param data 指向接收到的数据缓冲区的指针
 * @param dataLen 数据缓冲区的长度
 *
 * @return 返回值表示数据长度: -1, 数据异常; 0, 数据分片，需要继续接收; 其他, 已经处理的长度.
 */
int RlinkProcFrame(RlinkCtrlInfo *ctrl, uint8_t *data, int dataLen) {
    FrameHead *frameHead = (FrameHead *)data;
    size_t payloadLen = 0;
    uint8_t frameType;
    size_t procLen = 0;

    if (dataLen < sizeof(FrameHead)) {
        return procLen;
    }

    if (RlinkCheckMsgValid(frameHead, &frameType, &payloadLen) != SK_RET_SUCCESS) {
        ESP_LOGI(TAG, "Remote msg invalid: headFlag=%02x, headLen=%d, frameType=%d, msgType=%d, payloadLen=%d, proclen=%d", 
            ntohs(frameHead->headFlag), ntohs(frameHead->headLen), frameHead->frameType, frameHead->msgType, payloadLen, dataLen);
        return -1;
    }

    if (payloadLen > (dataLen - sizeof(FrameHead))) {
        return procLen;
    }

    if (frameType == FRAME_DPKT_TERM_AND_RELAY) {
        ESP_LOGD(TAG, "Recv data pkt data length=%u, RefFrameSize=%u, dataLen=%d!", payloadLen, sizeof(DataFrame), dataLen);
        RlinkPlayAudioData(ctrl, data, payloadLen);
        if (ctrl->roleMode == SK_ROLE_MODE_CALLEE && ctrl->offhookSendCnt == 0) {
            RlinkSendInnerEvent(ctrl, RLINK_EVENT_OFFHOOK, 0);
            ctrl->offhookSendCnt = 1;
            SkSmSendEvent(ctrl->handler, SM_EVENT_LINK, SM_EVENT_RLINK_PEER_CONNECT, ctrl->sessionID, 0);
        }
        procLen = sizeof(FrameHead) + payloadLen;
        ESP_LOGD(TAG, "Process message length1 %d", procLen);
        return procLen;
    }

    procLen = payloadLen + sizeof(FrameHead);
    if (frameHead->msgType == MSG_TERM_TO_TERM_OFFHOOK) {
        ESP_LOGI(TAG, "Recv offhook msg!");
        SkSmSendEvent(ctrl->handler, SM_EVENT_LINK, SM_EVENT_RLINK_PEER_CONNECT, ctrl->sessionID, 0);
    } else if (frameHead->msgType == MSG_TERM_TO_TERM_ONHOOK) {
        ESP_LOGI(TAG, "Recv onhook msg!");
        ctrl->rxExitReason = RLINK_EXIT_BY_CLOSE_BY_PEER;
    } else if (frameHead->msgType == MSG_TERM_TO_TERM_ALIGN_AUDIO_REPLY) {
        ESP_LOGI(TAG, "Recv align audio reply msg!");
        SkSmSendEvent(ctrl->handler, SM_EVENT_LINK, SM_EVENT_RLINK_ALIGN_REPLY, ctrl->sessionID, 0);
    } else if (frameHead->msgType == MSG_TERM_TO_CLOUD_AI_CALLEE_MATCH_REQUEST_RESP) {
        SkClinkSetCalleeId(((CtrlFrameCalleeMatch *)data)->ctrlMsg.calleeNameList);
        ESP_LOGI(TAG, "Recv callee result %s!", ((CtrlFrameCalleeMatch *)data)->ctrlMsg.calleeNameList);
        ctrl->codedDataEndCallback(ctrl->decPrivate, ctrl->sessionID);
        ctrl->rxExitReason = RLINK_EXIT_BY_NEW_LINK;
    } else if (frameHead->msgType == MSG_CLOUD_AI_TO_TERM_CALLEE_VOL_UP) {
        ESP_LOGI(TAG, "Recv vol up from AI!");
        SkSmSendEvent(ctrl->handler, SM_EVENT_LINK, SM_EVENT_RLINK_VOLUP, 0, 0);
    } else if (frameHead->msgType == MSG_CLOUD_AI_TO_TERM_CALLEE_VOL_DOWN) {
        ESP_LOGI(TAG, "Recv vol down from AI!");
        SkSmSendEvent(ctrl->handler, SM_EVENT_LINK, SM_EVENT_RLINK_VOLDOWN, 0, 0);
    } else if (frameHead->msgType == MSG_CLOUD_AI_TO_TERM_DIST_CHAT) {
        ESP_LOGI(TAG, "Recv distribute AI !");
        ctrl->rxExitReason = RLINK_EXIT_BY_NEW_AGENT;
    } else {
        ESP_LOGI(TAG, "Unknown msg type:%d", frameHead->msgType);
    }
    ESP_LOGD(TAG, "Process message length2 %d", procLen);
    return procLen;
}

int RlinkProcSockData(RlinkCtrlInfo *ctrl, uint8_t *buffer, int dataLen) {
    int ret, pos, procLen;

    pos = 0;
    procLen = 0;
    while (dataLen >= sizeof(FrameHead)) {
        ret = RlinkProcFrame(ctrl, &buffer[pos], dataLen);
        if (ret < 0) {
            ESP_LOGE(TAG, "RlinkProcFrame error pos=%d, dataLen=%d!", pos, dataLen);
            return -1;
        }
        if (ret == 0) {
            break;
        }
        pos += ret;
        dataLen -= ret;
        procLen += ret;
        ESP_LOGD(TAG, "Pos=%d, dataLen=%d, procLen=%d", pos, dataLen, procLen);
    }

    return procLen;
}

int RlinkProcLocalMsg(RlinkCtrlInfo *ctrl, TickType_t ticks) {
    int ret = SK_RET_SUCCESS;
    RlinkMsg msg;

    if (xQueueReceive(ctrl->msgQueue, &msg, ticks) != pdPASS) {
        return ret;
    }
    if (msg.event != RLINK_EVENT_TX_DATA) {
        ESP_LOGD(TAG, "RlinkProcLocalMsg: event = %d", msg.event);
    }
    switch (msg.event) {
        case RLINK_EVENT_START_CALLER_CMD:
            ctrl->sessionID = msg.param1;
            RlinkStartCall(ctrl);
            ESP_LOGI(TAG, "Start call cmd received, ip = %s, port = %d", ctrl->serverIp, ctrl->port);
            break;
        case RLINK_EVENT_LINK_CALL_AGENT:
            // 连接呼叫分析服务器
            ctrl->sessionID = msg.param1;
            RlinkLinkCallAgent(ctrl);
            break;
        case RLINK_EVENT_STOP_CALL:
            ctrl->linkFlag = RLINK_LINK_STOP;
            break;
        case RLINK_EVENT_OFFHOOK:
            RlinkSendOnhookOrOffhook(ctrl, MSG_TERM_TO_TERM_OFFHOOK);
            RlinkSendOnhookOrOffhook(ctrl, MSG_TERM_TO_TERM_OFFHOOK);
            RlinkSendOnhookOrOffhook(ctrl, MSG_TERM_TO_TERM_OFFHOOK);
            RlinkSendOnhookOrOffhook(ctrl, MSG_TERM_TO_TERM_OFFHOOK);
            RlinkSendOnhookOrOffhook(ctrl, MSG_TERM_TO_TERM_OFFHOOK);
            break;
        case RLINK_EVENT_ONHOOK:
            ret = RlinkSendOnhookOrOffhook(ctrl, MSG_TERM_TO_TERM_ONHOOK);
            break;
        case RLINK_EVENT_TX_DATA:
            RlinkSendAudioData(ctrl, (SkAudioBuf *)msg.arg, msg.timestamp);
            break;
        case RLINK_EVENT_RX_EXIT:
            ESP_LOGI(TAG, "Rlink rx stopped, close socket");
            RlinkStopCall(ctrl);
            if (msg.param1 == RLINK_EXIT_BY_NEW_LINK) {
                SkSmSendEvent(ctrl->handler, SM_EVENT_LINK, SM_EVENT_RLINK_CALLEE_MATCH, ctrl->sessionID, 1);
            } else if (msg.param1 == RLINK_EXIT_BY_NEW_AGENT) {
                SkSmSendEvent(ctrl->handler, SM_EVENT_CMD, SPEECH_CMD_EVENT_CHAT, 0, 0);
            } else {
                SkSmSendEvent(ctrl->handler, SM_EVENT_LINK, SM_EVENT_RLINK_DISCONNECT, ctrl->sessionID, 0);
            }     
            break;
        case RLINK_EVENT_START_CALLEE_CMD:
            ctrl->sessionID = msg.param1;
            RlinkStartCall(ctrl);
            ESP_LOGI(TAG, "Start call cmd received, ip = %s, port = %d", ctrl->serverIp, ctrl->port);
            break;
        case RLINK_EVENT_ALIGN_AUDIO:
            ESP_LOGD(TAG, "Align audio");
            RlinkAlignAudio(ctrl);
            break;
        default:
            ESP_LOGI(TAG, "Unknown event:%d", msg.event);
    }

    return ret;
}

#define RLINK_TIMEOUT_COUNT 450
void RlinkRxLoop(RlinkCtrlInfo *ctrl) {
    int bytes, lastBytes, bufSize, procLen, startPos, rxPos, freeSize;
    int sock;
    uint8_t *buffer = NULL;

    bufSize = RLINK_RX_BUFF_SIZE;
    buffer = (uint8_t *)malloc(bufSize);
    if (buffer == NULL) {
        ESP_LOGI(TAG, "malloc buffer failed!");
        return;
    }
    ESP_LOGI(TAG, "Rlink Rx buffer %p size is %d", buffer, bufSize);

    sock = ctrl->sock;
    lastBytes = 0;
    startPos = 0;
    rxPos = 0;
    freeSize = bufSize;
    ctrl->rxExitReason = 0;
    while (ctrl->linkFlag == RLINK_LINK_RUN) {
        if (ctrl->rxExitReason != 0) {
            break;
        }
        // Socket有数据可读
        bytes = recv(sock, &buffer[rxPos], freeSize, 0);
        if (bytes <= 0) {
            ctrl->rxTimeoutCnt++;
            if (ctrl->rxTimeoutCnt >= RLINK_TIMEOUT_COUNT) {
                ESP_LOGE(TAG, "Recv timeout %d times %d close socket!",
                    ctrl->rxTimeoutCnt, bytes);
                ctrl->disconnectFlag = RLINK_DISCONNECT;
                break;
            }
            continue;
        } else {
            ctrl->rxTimeoutCnt = 0;
        }
        lastBytes += bytes;
        freeSize -= bytes;
        rxPos += bytes;
        procLen = RlinkProcSockData(ctrl, &buffer[startPos], lastBytes);
        if (procLen < 0 || procLen > lastBytes) {
            ESP_LOGI(TAG, "RlinkProcSockData failed startPos=%d, lastBytes=%d!", startPos, lastBytes);
            break;
        } else if (procLen == lastBytes) {
            startPos = 0;
            lastBytes = 0;
            rxPos = 0;
            freeSize = bufSize;
        } else if (procLen > 0) {
            startPos += procLen;
            lastBytes -= procLen;
            if (freeSize + lastBytes >= sizeof(DataFrame)) {
                continue;
            }
            if (lastBytes < startPos) {
                memcpy(buffer, &buffer[startPos], lastBytes);
                //ESP_LOGI(TAG, "RlinkRxLoop: move startPos=%d, lastBytes=%d, freeSize=%d!", startPos, lastBytes, freeSize);
                startPos = 0;
                freeSize = bufSize - lastBytes;
                rxPos = lastBytes;
                ESP_LOGI(TAG, "RlinkRxLoop: move startPos=%d, lastBytes=%d, freeSize=%d!", startPos, lastBytes, freeSize);
            } else {
                ESP_LOGE(TAG, "RlinkRxLoop: startPos=%d, lastBytes=%d, freeSize=%d!", startPos, lastBytes, freeSize);
                break;
            }
        }
    }
    free(buffer);
    RlinkSendInnerEvent(ctrl, RLINK_EVENT_RX_EXIT, ctrl->rxExitReason);
    ctrl->rxExitReason = 0;
}

void SkRlinkSetFunFlag(uint8_t flag) {
    g_rlinkCtrl.taskFlag = flag;
}

void RlinkRxTask(void *arg) {
    RlinkCtrlInfo *ctrl = &g_rlinkCtrl;

    while (ctrl->taskFlag == RLINK_TASK_RUN) {
        if (ctrl->linkFlag == RLINK_LINK_STOP || ctrl->disconnectFlag == RLINK_DISCONNECT) {
            vTaskDelay(100); // 等待连接成功标志位被设置
            continue;
        }
        RlinkRxLoop(ctrl);
    }
    
    vTaskDelete(NULL);
}

void RlinkMainTask(void *arg) {
    RlinkCtrlInfo *ctrl = &g_rlinkCtrl;
    TickType_t ticks = pdMS_TO_TICKS(100);

    while (ctrl->taskFlag == RLINK_TASK_RUN) {
        // 检查 队列中是否有消息
        if (RlinkProcLocalMsg(ctrl, ticks) != SK_RET_SUCCESS) {
            break;
        }
    }
    vTaskDelete(NULL);
}

void SkRlinkFeedWebSocketAudio(void *arg, void *data, uint16_t len) {
    RlinkCtrlInfo *ctrl = &g_rlinkCtrl;
    uint8_t *frameBuffer = NULL;
    DataFrame *frame = NULL;
    SkAudioDownlinkTimeRecord *timeRecord;
    uint16_t totalLen;
    static uint16_t seqNum = 0;

    // 数据有效性检查
    if (data == NULL || len == 0) {
        ESP_LOGE(TAG, "Invalid WebSocket audio data: len=%d", len);
        return;
    }

    // 计算需要的总长度：FrameHead + timeRecord + 4字节头部 + audioData
    totalLen = sizeof(FrameHead) + sizeof(SkAudioDownlinkTimeRecord) + 4 + len;

    // 分配临时缓冲区
    frameBuffer = (uint8_t *)malloc(totalLen);
    if (frameBuffer == NULL) {
        ESP_LOGE(TAG, "Failed to allocate frame buffer");
        return;
    }

    frame = (DataFrame *)frameBuffer;

    // 构造FrameHead（模拟TCP接收的格式）
    frame->frameHead.headFlag = htons(FRAME_HEAD_FLAG);
    frame->frameHead.frameType = FRAME_DPKT_TERM_AND_RELAY;
    frame->frameHead.msgType = MSG_TYPE_INVALID;
    frame->frameHead.payloadLen = htons(sizeof(SkAudioDownlinkTimeRecord) + 4 + len);
    frame->frameHead.seqID = htons(seqNum++);

    // 构造时间记录
    timeRecord = (SkAudioDownlinkTimeRecord *)frame->timeRecord;
    memset(timeRecord, 0, sizeof(SkAudioDownlinkTimeRecord));
    timeRecord->seq = seqNum - 1;
    timeRecord->len = 4 + len;  // 包含4字节头部的总长度
    timeRecord->decRxTick = SkOsGetTickCnt();

    // 添加4字节头部（与TCP数据格式保持一致）
    uint8_t *audioDataPos = frameBuffer + sizeof(FrameHead) + sizeof(SkAudioDownlinkTimeRecord);
    audioDataPos[0] = seqNum & 0xFF;
    audioDataPos[1] = (seqNum >> 8) & 0xFF;
    audioDataPos[2] = len & 0xFF;
    audioDataPos[3] = (len >> 8) & 0xFF;
  
    memcpy(audioDataPos + 4, data, len);

    // 调用现有的音频处理函数
    RlinkPlayAudioData(ctrl, frameBuffer,
                       sizeof(SkAudioDownlinkTimeRecord) + 4 + len);

    // 释放临时缓冲区
    free(frameBuffer);

    return;
}

void SkRlinkFeedReordAudio(void *handler, const uint8_t *data, size_t len, uint32_t timestamp) {
    RlinkCtrlInfo *ctrl = (RlinkCtrlInfo *)handler;
    SkAudioBuf *audioBuf;

    if (ctrl->linkFlag != RLINK_LINK_RUN) {
        return;
    }
    audioBuf = SkAudioBufferGetFree(ctrl->recordQueue, 0);
    if (audioBuf == NULL) {
        ctrl->statRecordBufFail++;
        return;
    }
    if (audioBuf->size < (len + audioBuf->offset + 32)) {
        ESP_LOGE(TAG, "SkRlinkFeedReordAudio: audioBuf->size=%d < len=%u", audioBuf->size, len);
        SkAudioBufferPutFree(ctrl->recordQueue, audioBuf);
        return;
    }
    memcpy((int8_t *)&audioBuf->data[audioBuf->offset + 32], data, len);
    audioBuf->length = len;
    if (SkRlinkSendAudioData(audioBuf, timestamp) != SK_RET_SUCCESS) {
        ESP_LOGE(TAG, "SkRlinkSendAudioData failed!");
        SkAudioBufferPutFree(ctrl->recordQueue, audioBuf);
        ctrl->statRecordEnqueueFail++;
    } else {
        ctrl->statRecordEnqueueCnt++;
    }
    return;
}

void SkRlinkStartTasks() {
    g_rlinkCtrl.taskFlag = RLINK_TASK_RUN;
    xTaskCreate(RlinkRxTask, "RlinkRxTask", 4096, NULL, 5, &g_rlinkCtrl.rxTaskHandle);
    ESP_LOGI(TAG, "stack base %p", pxTaskGetStackStart(g_rlinkCtrl.rxTaskHandle));
    xTaskCreate(RlinkMainTask, "RlinkMainTask", 4096, NULL, 5, &g_rlinkCtrl.txTaskHandle);
    ESP_LOGI(TAG, "stack base %p", pxTaskGetStackStart(g_rlinkCtrl.txTaskHandle));
    return;
}

SkRlinkHandler SkRlinkInit(SkStateHandler handler, int32_t bytesPerSample, int32_t txChunkSize, int32_t rxChunkSize) {
    if (RlinkInit(handler, bytesPerSample, txChunkSize, rxChunkSize) != SK_RET_SUCCESS) {
        return NULL;
    }
    SkRlinkStartTasks();

    return &g_rlinkCtrl;
}

void SkRlinkSetCodedDataCallback(SkRlinkCodedDataCallback callback, void *private) {
    RlinkCtrlInfo *ctrl = &g_rlinkCtrl;
    ctrl->codedDataCallback = callback;
    ctrl->decPrivate = private;
    return;
}

void SkRlinkSetCodedDataEndCallback(SkRlinkCodedDataEndCallback callback, void *private) {
    RlinkCtrlInfo *ctrl = &g_rlinkCtrl;
    ctrl->codedDataEndCallback = callback;
    ctrl->decPrivate = private;
    return;
}

SkRlinkHandler SkRlinkGetHandler() {
    return &g_rlinkCtrl;
}

void SkRlinkSetPm(bool flag) {
    if (flag) {
#if CONFIG_SK_PM_SLEEP_MODE == CONFIG_SK_PM_SLEEP_MODE_LIGHT2
        g_rlinkCtrl.taskFlag = RLINK_TASK_STOP;
#endif
    } else {
#if CONFIG_SK_PM_SLEEP_MODE == CONFIG_SK_PM_SLEEP_MODE_LIGHT2
        SkRlinkStartTasks();
#endif
    }
}

void SkRlinkSetCallAgent(char *callAgent, uint16_t callAgentPort) {
    strcpy(g_rlinkCtrl.callAgentIp, callAgent);
    g_rlinkCtrl.callAgentPort = callAgentPort;
    return;
}
