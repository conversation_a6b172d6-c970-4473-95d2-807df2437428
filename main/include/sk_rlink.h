/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_rlink.h
 * @description: Relay链路接口.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#ifndef SK_RLINK_H
#define SK_RLINK_H
#include <stdint.h>
#include "sk_sm.h"

enum {
    RLINK_LINK_RUN = 0,
    RLINK_LINK_STOP = 1,
};

enum {
    RLINK_TASK_RUN = 0,
    RLINK_TASK_STOP = 1,
};

enum {
    RLINK_EVENT_START_CALLER_CMD = 1,
    RLINK_EVENT_STOP_CALL = 2,
    RLINK_EVENT_OFFHOOK = 3,
    RLINK_EVENT_ONHOOK = 4,
    RLINK_EVENT_TX_DATA = 5,
    RLINK_EVENT_RX_EXIT = 6,
    RLINK_EVENT_START_CALLEE_CMD = 7,
    RL<PERSON><PERSON>_EVENT_ALIGN_AUDIO = 8,
    RL<PERSON><PERSON>_EVENT_LINK_CALL_AGENT = 9,
};

typedef int32_t (*SkRlinkCodedDataCallback)(void *private, uint16_t sessionId,
    uint8_t *data, int32_t len, SkAudioDownlinkTimeRecord *timeRecord);

typedef void (*SkRlinkCodedDataEndCallback)(void *private, uint16_t sessionId);

typedef void* SkRlinkHandler;

void RlinkSetCallReqInfo(uint32_t roleMode, uint8_t serverIp[4], uint16_t port, void *ctrlMsg);
SkRlinkHandler SkRlinkInit(SkStateHandler handler, int32_t bytesPerSample, int32_t txChunkSize, int32_t rxChunkSize);
int SkRlinkEventNotify(uint8_t event, uint16_t param1);
void SkRlinkFeedReordAudio(void *handler, const uint8_t *data, size_t len, uint32_t timestamp);
void SkRlinkFeedWebSocketAudio(void *arg, void *data, uint16_t len);
void SkRlinkSetCodedDataCallback(SkRlinkCodedDataCallback handler, void *private);
void SkRlinkSetCodedDataEndCallback(SkRlinkCodedDataEndCallback handler, void *private);
SkRlinkHandler SkRlinkGetHandler();
void SkRlinkShowStat();
void SkRlinkSetFunFlag(uint8_t flag);
void SkRlinkSetPm(bool flag);
void SkRlinkSetCallAgent(char *callAgent, uint16_t callAgentPort);

#endif //SK_RLINK_H